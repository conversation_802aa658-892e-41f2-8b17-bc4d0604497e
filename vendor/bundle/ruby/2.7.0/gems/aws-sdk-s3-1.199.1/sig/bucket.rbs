# WARNING ABOUT GENERATED CODE
#
# This file is generated. See the contributing guide for more information:
# https://github.com/aws/aws-sdk-ruby/blob/version-3/CONTRIBUTING.md
#
# WARNING ABOUT GENERATED CODE

module Aws
  module S3
    # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html
    class Bucket
      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#initialize-instance_method
      def initialize: (String name, Hash[Symbol, untyped] options) -> void
                    | (name: String, ?client: Client) -> void
                    | (Hash[Symbol, untyped] args) -> void

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#name-instance_method
      def name: () -> String

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#creation_date-instance_method
      def creation_date: () -> ::Time

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#bucket_region-instance_method
      def bucket_region: () -> ::String

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#bucket_arn-instance_method
      def bucket_arn: () -> ::String

      def client: () -> Client


      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#data-instance_method
      def data: () -> Types::Bucket

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#data_loaded?-instance_method
      def data_loaded?: () -> bool

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#exists?-instance_method
      def exists?: (?max_attempts: Integer, ?delay: Numeric, ?before_attempt: (^(Integer attempts) -> void), ?before_wait: (^(Integer attempts, untyped response) -> void)) -> bool
                 | (?Hash[Symbol, untyped]) -> bool

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#wait_until_exists-instance_method
      def wait_until_exists: (?max_attempts: Integer, ?delay: Numeric, ?before_attempt: (^(Integer attempts) -> void), ?before_wait: (^(Integer attempts, untyped response) -> void)) ?{ (untyped waiter) -> void } -> Bucket
                           | (?Hash[Symbol, untyped]) ?{ (untyped waiter) -> void } -> Bucket

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#wait_until_not_exists-instance_method
      def wait_until_not_exists: (?max_attempts: Integer, ?delay: Numeric, ?before_attempt: (^(Integer attempts) -> void), ?before_wait: (^(Integer attempts, untyped response) -> void)) ?{ (untyped waiter) -> void } -> Bucket
                               | (?Hash[Symbol, untyped]) ?{ (untyped waiter) -> void } -> Bucket

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#create-instance_method
      def create: (
                    ?acl: ("private" | "public-read" | "public-read-write" | "authenticated-read"),
                    ?create_bucket_configuration: {
                      location_constraint: ("af-south-1" | "ap-east-1" | "ap-northeast-1" | "ap-northeast-2" | "ap-northeast-3" | "ap-south-1" | "ap-south-2" | "ap-southeast-1" | "ap-southeast-2" | "ap-southeast-3" | "ap-southeast-4" | "ap-southeast-5" | "ca-central-1" | "cn-north-1" | "cn-northwest-1" | "EU" | "eu-central-1" | "eu-central-2" | "eu-north-1" | "eu-south-1" | "eu-south-2" | "eu-west-1" | "eu-west-2" | "eu-west-3" | "il-central-1" | "me-central-1" | "me-south-1" | "sa-east-1" | "us-east-2" | "us-gov-east-1" | "us-gov-west-1" | "us-west-1" | "us-west-2")?,
                      location: {
                        type: ("AvailabilityZone" | "LocalZone")?,
                        name: ::String?
                      }?,
                      bucket: {
                        data_redundancy: ("SingleAvailabilityZone" | "SingleLocalZone")?,
                        type: ("Directory")?
                      }?,
                      tags: Array[
                        {
                          key: ::String,
                          value: ::String
                        },
                      ]?
                    },
                    ?grant_full_control: ::String,
                    ?grant_read: ::String,
                    ?grant_read_acp: ::String,
                    ?grant_write: ::String,
                    ?grant_write_acp: ::String,
                    ?object_lock_enabled_for_bucket: bool,
                    ?object_ownership: ("BucketOwnerPreferred" | "ObjectWriter" | "BucketOwnerEnforced")
                  ) -> Types::CreateBucketOutput
                | (?Hash[Symbol, untyped]) -> Types::CreateBucketOutput

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#delete-instance_method
      def delete: (
                    ?expected_bucket_owner: ::String
                  ) -> ::Aws::EmptyStructure
                | (?Hash[Symbol, untyped]) -> ::Aws::EmptyStructure

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#delete_objects-instance_method
      def delete_objects: (
                            delete: {
                              objects: Array[
                                {
                                  key: ::String,
                                  version_id: ::String?,
                                  etag: ::String?,
                                  last_modified_time: ::Time?,
                                  size: ::Integer?
                                },
                              ],
                              quiet: bool?
                            },
                            ?mfa: ::String,
                            ?request_payer: ("requester"),
                            ?bypass_governance_retention: bool,
                            ?expected_bucket_owner: ::String,
                            ?checksum_algorithm: ("CRC32" | "CRC32C" | "SHA1" | "SHA256" | "CRC64NVME")
                          ) -> Types::DeleteObjectsOutput
                        | (?Hash[Symbol, untyped]) -> Types::DeleteObjectsOutput

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#put_object-instance_method
      def put_object: (
                        ?acl: ("private" | "public-read" | "public-read-write" | "authenticated-read" | "aws-exec-read" | "bucket-owner-read" | "bucket-owner-full-control"),
                        ?body: ::String | ::StringIO | ::File,
                        ?cache_control: ::String,
                        ?content_disposition: ::String,
                        ?content_encoding: ::String,
                        ?content_language: ::String,
                        ?content_length: ::Integer,
                        ?content_md5: ::String,
                        ?content_type: ::String,
                        ?checksum_algorithm: ("CRC32" | "CRC32C" | "SHA1" | "SHA256" | "CRC64NVME"),
                        ?checksum_crc32: ::String,
                        ?checksum_crc32c: ::String,
                        ?checksum_crc64nvme: ::String,
                        ?checksum_sha1: ::String,
                        ?checksum_sha256: ::String,
                        ?expires: ::Time,
                        ?if_match: ::String,
                        ?if_none_match: ::String,
                        ?grant_full_control: ::String,
                        ?grant_read: ::String,
                        ?grant_read_acp: ::String,
                        ?grant_write_acp: ::String,
                        key: ::String,
                        ?write_offset_bytes: ::Integer,
                        ?metadata: Hash[::String, ::String],
                        ?server_side_encryption: ("AES256" | "aws:fsx" | "aws:kms" | "aws:kms:dsse"),
                        ?storage_class: ("STANDARD" | "REDUCED_REDUNDANCY" | "STANDARD_IA" | "ONEZONE_IA" | "INTELLIGENT_TIERING" | "GLACIER" | "DEEP_ARCHIVE" | "OUTPOSTS" | "GLACIER_IR" | "SNOW" | "EXPRESS_ONEZONE" | "FSX_OPENZFS"),
                        ?website_redirect_location: ::String,
                        ?sse_customer_algorithm: ::String,
                        ?sse_customer_key: ::String,
                        ?sse_customer_key_md5: ::String,
                        ?ssekms_key_id: ::String,
                        ?ssekms_encryption_context: ::String,
                        ?bucket_key_enabled: bool,
                        ?request_payer: ("requester"),
                        ?tagging: ::String,
                        ?object_lock_mode: ("GOVERNANCE" | "COMPLIANCE"),
                        ?object_lock_retain_until_date: ::Time,
                        ?object_lock_legal_hold_status: ("ON" | "OFF"),
                        ?expected_bucket_owner: ::String
                      ) -> Object
                    | (?Hash[Symbol, untyped]) -> Object

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#acl-instance_method
      def acl: () -> BucketAcl

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#cors-instance_method
      def cors: () -> BucketCors

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#lifecycle-instance_method
      def lifecycle: () -> BucketLifecycle

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#lifecycle_configuration-instance_method
      def lifecycle_configuration: () -> BucketLifecycleConfiguration

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#logging-instance_method
      def logging: () -> BucketLogging

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#multipart_uploads-instance_method
      def multipart_uploads: (
                               ?delimiter: ::String,
                               ?encoding_type: ("url"),
                               ?key_marker: ::String,
                               ?prefix: ::String,
                               ?upload_id_marker: ::String,
                               ?expected_bucket_owner: ::String,
                               ?request_payer: ("requester")
                             ) -> MultipartUpload::Collection
                           | (?Hash[Symbol, untyped]) -> MultipartUpload::Collection

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#notification-instance_method
      def notification: () -> BucketNotification

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#object-instance_method
      def object: (String key) -> Object

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#object_versions-instance_method
      def object_versions: (
                             ?delimiter: ::String,
                             ?encoding_type: ("url"),
                             ?key_marker: ::String,
                             ?prefix: ::String,
                             ?version_id_marker: ::String,
                             ?expected_bucket_owner: ::String,
                             ?request_payer: ("requester"),
                             ?optional_object_attributes: Array[("RestoreStatus")]
                           ) -> ObjectVersion::Collection
                         | (?Hash[Symbol, untyped]) -> ObjectVersion::Collection

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#objects-instance_method
      def objects: (
                     ?delimiter: ::String,
                     ?encoding_type: ("url"),
                     ?prefix: ::String,
                     ?fetch_owner: bool,
                     ?start_after: ::String,
                     ?request_payer: ("requester"),
                     ?expected_bucket_owner: ::String,
                     ?optional_object_attributes: Array[("RestoreStatus")]
                   ) -> ObjectSummary::Collection
                 | (?Hash[Symbol, untyped]) -> ObjectSummary::Collection

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#policy-instance_method
      def policy: () -> BucketPolicy

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#request_payment-instance_method
      def request_payment: () -> BucketRequestPayment

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#tagging-instance_method
      def tagging: () -> BucketTagging

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#versioning-instance_method
      def versioning: () -> BucketVersioning

      # https://docs.aws.amazon.com/sdk-for-ruby/v3/api/Aws/S3/Bucket.html#website-instance_method
      def website: () -> BucketWebsite

      class Collection < ::Aws::Resources::Collection[Bucket]
      end
    end
  end
end
