current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/bootsnap-1.18.6/ext/bootsnap
/Users/<USER>/.rbenv/versions/2.7.8/bin/ruby -I /Users/<USER>/.rbenv/versions/2.7.8/lib/ruby/2.7.0 -r ./siteconf20251001-64459-1h73ggp.rb extconf.rb
checking for fdatasync() in unistd.h... yes
checking for whether -D_GNU_SOURCE is accepted as CPPFLAGS... yes
checking for whether -O3 is accepted as CFLAGS... yes
checking for whether -std=c99 is accepted as CFLAGS... yes
creating Makefile

current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/bootsnap-1.18.6/ext/bootsnap
make "DESTDIR=" clean

current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/bootsnap-1.18.6/ext/bootsnap
make "DESTDIR="
compiling bootsnap.c
linking shared-object bootsnap/bootsnap.bundle
ld: warning: -multiply_defined is obsolete
ld: warning: ignoring duplicate libraries: '-lruby.2.7'

current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/bootsnap-1.18.6/ext/bootsnap
make "DESTDIR=" install
/usr/bin/install -c -m 0755 bootsnap.bundle ./.gem.20251001-64459-1owqg48/bootsnap
