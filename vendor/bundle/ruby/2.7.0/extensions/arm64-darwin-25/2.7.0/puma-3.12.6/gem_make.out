current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/puma-3.12.6/ext/puma_http11
/Users/<USER>/.rbenv/versions/2.7.8/bin/ruby -I /Users/<USER>/.rbenv/versions/2.7.8/lib/ruby/2.7.0 -r ./siteconf20251001-64459-zgkxbr.rb extconf.rb
checking for BIO_read() in -lcrypto... yes
checking for SSL_CTX_new() in -lssl... yes
checking for openssl/bio.h... yes
creating Makefile

current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/puma-3.12.6/ext/puma_http11
make "DESTDIR=" clean

current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/puma-3.12.6/ext/puma_http11
make "DESTDIR="
compiling http11_parser.c
compiling io_buffer.c
compiling mini_ssl.c
mini_ssl.c:220:27: warning: 'DTLSv1_method' is deprecated [-Wdeprecated-declarations]
  220 |   conn->ctx = SSL_CTX_new(DTLSv1_method());
      |                           ^
/Users/<USER>/.rbenv/versions/2.7.8/openssl/include/openssl/ssl.h:1905:1: note: 'DTLSv1_method' has been explicitly marked deprecated here
 1905 | DEPRECATEDIN_1_1_0(__owur const SSL_METHOD *DTLSv1_method(void)) /* DTLSv1.0 */
      | ^
/Users/<USER>/.rbenv/versions/2.7.8/openssl/include/openssl/opensslconf.h:163:34: note: expanded from macro 'DEPRECATEDIN_1_1_0'
  163 | # define DEPRECATEDIN_1_1_0(f)   DECLARE_DEPRECATED(f)
      |                                  ^
/Users/<USER>/.rbenv/versions/2.7.8/openssl/include/openssl/opensslconf.h:121:55: note: expanded from macro 'DECLARE_DEPRECATED'
  121 | #   define DECLARE_DEPRECATED(f)    f __attribute__ ((deprecated));
      |                                                       ^
1 warning generated.
compiling puma_http11.c
linking shared-object puma/puma_http11.bundle
ld: warning: -multiply_defined is obsolete
ld: warning: ignoring duplicate libraries: '-lruby.2.7'

current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/puma-3.12.6/ext/puma_http11
make "DESTDIR=" install
/usr/bin/install -c -m 0755 puma_http11.bundle ./.gem.20251001-64459-t82rpp/puma
