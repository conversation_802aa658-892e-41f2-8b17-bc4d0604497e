current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/oj-3.16.11/ext/oj
/Users/<USER>/.rbenv/versions/2.7.8/bin/ruby -I /Users/<USER>/.rbenv/versions/2.7.8/lib/ruby/2.7.0 -r ./siteconf20251001-64459-46xpy5.rb extconf.rb
>>>>> Creating Makefile for ruby version 2.7.8 on arm64-darwin25 <<<<<
checking for rb_gc_mark_movable()... yes
checking for stpcpy()... yes
checking for pthread_mutex_init()... yes
checking for getrlimit() in sys/resource.h... yes
checking for rb_enc_interned_str()... no
checking for rb_ext_ractor_safe() in ruby.h... no
creating Makefile

current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/oj-3.16.11/ext/oj
make "DESTDIR=" clean

current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/oj-3.16.11/ext/oj
make "DESTDIR="
compiling cache.c
compiling cache8.c
compiling circarray.c
compiling code.c
compiling compat.c
compiling custom.c
compiling debug.c
compiling dump.c
compiling dump_compat.c
compiling dump_leaf.c
compiling dump_object.c
compiling dump_strict.c
compiling encoder.c
compiling err.c
compiling fast.c
compiling intern.c
compiling mem.c
compiling mimic_json.c
compiling object.c
compiling odd.c
compiling oj.c
compiling parse.c
compiling parser.c
compiling rails.c
compiling reader.c
compiling resolve.c
compiling rxclass.c
compiling saj.c
compiling saj2.c
compiling scp.c
compiling sparse.c
compiling stream_writer.c
compiling strict.c
compiling string_writer.c
compiling trace.c
compiling usual.c
compiling util.c
compiling val_stack.c
compiling validate.c
compiling wab.c
linking shared-object oj/oj.bundle
ld: warning: -multiply_defined is obsolete
ld: warning: ignoring duplicate libraries: '-lruby.2.7'

current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/oj-3.16.11/ext/oj
make "DESTDIR=" install
/usr/bin/install -c -m 0755 oj.bundle ./.gem.20251001-64459-1k9xh5h/oj
