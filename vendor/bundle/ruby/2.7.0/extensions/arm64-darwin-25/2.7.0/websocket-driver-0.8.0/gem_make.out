current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/websocket-driver-0.8.0/ext/websocket-driver
/Users/<USER>/.rbenv/versions/2.7.8/bin/ruby -I /Users/<USER>/.rbenv/versions/2.7.8/lib/ruby/2.7.0 -r ./siteconf20251001-64459-13j1tw4.rb extconf.rb
creating Makefile

current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/websocket-driver-0.8.0/ext/websocket-driver
make "DESTDIR=" clean

current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/websocket-driver-0.8.0/ext/websocket-driver
make "DESTDIR="
compiling websocket_mask.c
linking shared-object websocket_mask.bundle
ld: warning: -multiply_defined is obsolete
ld: warning: ignoring duplicate libraries: '-lruby.2.7'

current directory: /Users/<USER>/Desktop/travelgator-backend/vendor/bundle/ruby/2.7.0/gems/websocket-driver-0.8.0/ext/websocket-driver
make "DESTDIR=" install
/usr/bin/install -c -m 0755 websocket_mask.bundle ./.gem.20251001-64459-1sv5vio
