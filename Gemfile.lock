GIT
  remote: https://github.com/kooinam/myinfo-rails
  revision: 2565377cc47060215bea1a6869a9229b61c8bb0c
  ref: 2565377cc47060215bea1a6869a9229b61c8bb0c
  specs:
    myinfo (0.5.0)
      jwe (~> 0.4)
      jwt (~> 2.2)

GIT
  remote: https://github.com/kooinam/stringex
  revision: 98a0d491a8ca250d0c221db23a9c05e078f01a89
  ref: 98a0d491a8ca250d0c221db23a9c05e078f01a89
  specs:
    stringex (2.8.5)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.0.8)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.0.3)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      arel (>= 9.0)
    activestorage (*******)
      actionpack (= *******)
      activerecord (= *******)
      marcel (~> 1.0.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 0.7, < 2)
      minitest (~> 5.1)
      tzinfo (~> 1.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    arel (9.0.0)
    aws-eventstream (1.4.0)
    aws-partitions (1.1168.0)
    aws-sdk-core (3.233.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      bigdecimal
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.113.0)
      aws-sdk-core (~> 3, >= 3.231.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.199.1)
      aws-sdk-core (~> 3, >= 3.231.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.1)
      aws-eventstream (~> 1, >= 1.0.2)
    axlsx (2.1.0.pre)
      htmlentities (~> 4.3.1)
      nokogiri (>= 1.4.1)
      rubyzip (~> 1.1.7)
    axlsx_rails (0.5.2)
      actionpack (>= 3.1)
      axlsx (>= 2.0.1)
    base64 (0.3.0)
    bcrypt (3.1.20)
    better_errors (2.10.1)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
      rouge (>= 1.0.0)
    bigdecimal (3.2.3)
    binding_of_caller (1.0.1)
      debug_inspector (>= 1.2.0)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    bson (5.1.1)
    builder (3.3.0)
    bumbler (0.9.0)
    byebug (11.1.3)
    cgi (0.5.0)
    chronic (0.10.2)
    colorize (1.1.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.4)
    crass (1.0.6)
    csv (3.3.5)
    database_cleaner (2.1.0)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.2.2)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    debug_inspector (1.2.0)
    declarative (0.0.20)
    device_detector (1.1.3)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.6.2)
    digest-crc (0.7.0)
      rake (>= 12.0.0, < 14.0.0)
    domain_name (0.6.20240107)
    dotenv (2.8.1)
    dotenv-rails (2.8.1)
      dotenv (= 2.8.1)
      railties (>= 3.2)
    erubi (1.13.1)
    exception_notification (4.6.0)
      actionmailer (>= 5.2, < 9)
      activesupport (>= 5.2, < 9)
    excon (1.2.5)
      logger
    faker (3.4.2)
      i18n (>= 1.8.11, < 2)
    faraday (2.8.1)
      base64
      faraday-net_http (>= 2.0, < 3.1)
      ruby2_keywords (>= 0.0.4)
    faraday-multipart (1.1.1)
      multipart-post (~> 2.0)
    faraday-net_http (3.0.2)
    ffi (1.17.2-arm64-darwin)
    fog-aws (3.33.0)
      base64 (>= 0.2, < 0.4)
      fog-core (~> 2.6)
      fog-json (~> 1.1)
      fog-xml (~> 0.1)
    fog-core (2.6.0)
      builder
      excon (~> 1.0)
      formatador (>= 0.2, < 2.0)
      mime-types
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-xml (0.1.5)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    formatador (1.2.1)
      reline
    friendly_id (5.5.1)
      activerecord (>= 4.0.0)
    globalid (1.1.0)
      activesupport (>= 5.0)
    google-apis-core (0.16.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.3, < 3.a)
      mini_mime (~> 1.0)
      mutex_m
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
    google-apis-identitytoolkit_v3 (0.17.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-cloud-env (2.1.1)
      faraday (>= 1.0, < 3.a)
    googleauth (1.11.2)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    haml (6.3.0)
      temple (>= 0.8.2)
      thor
      tilt
    hash_diff (1.1.1)
    hashie (5.0.0)
    htmlentities (4.3.4)
    http-accept (1.7.0)
    http-cookie (1.1.0)
      domain_name (~> 0.5)
    httparty (0.23.1)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.9.0)
      mutex_m
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    io-console (0.8.1)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    json (2.15.0)
    jwe (0.4.0)
    jwt (2.10.2)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    kaminari-mongoid (1.0.2)
      kaminari-core (~> 1.0)
      mongoid
    koala (3.7.0)
      addressable
      base64
      cgi
      faraday
      faraday-multipart
      json (>= 1.8)
      ostruct
      rexml
    listen (3.1.5)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
      ruby_dep (~> 1.2)
    logger (1.7.0)
    lograge (0.14.0)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.3)
    method_source (1.1.0)
    mime-types (3.7.0)
      logger
      mime-types-data (~> 3.2025, >= 3.2025.0507)
    mime-types-data (3.2025.0924)
    mini_magick (5.3.1)
      logger
    mini_mime (1.1.5)
    minitest (5.25.5)
    monetize (1.13.0)
      money (~> 6.12)
    money (6.19.0)
      i18n (>= 0.6.4, <= 2)
    money-rails (1.15.0)
      activesupport (>= 3.0)
      monetize (~> 1.9)
      money (~> 6.13)
      railties (>= 3.0)
    mongo (2.21.3)
      base64
      bson (>= 4.14.1, < 6.0.0)
    mongoid (6.4.0)
      activemodel (>= 5.1, < 6.0.0)
      mongo (>= 2.5.1, < 3.0.0)
    mongoid-autoinc (6.0.4)
      mongoid (>= 6.0, < 9.0)
    mongoid-compatibility (1.0.0)
      activesupport
      mongoid (>= 2.0)
    mongoid-slug (6.0.1)
      mongoid (>= 3.0)
      mongoid-compatibility
      stringex (~> 2.0)
    mongoid_enumerable (0.4.4)
      mongoid (>= 4.0)
    msgpack (1.4.5)
    multi_json (1.15.0)
    multi_xml (0.6.0)
    multipart-post (2.4.1)
    mutex_m (0.3.0)
    net-imap (0.4.22)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    netrc (0.11.0)
    nio4r (2.7.4)
    nkf (0.2.0)
    nokogiri (1.15.7-arm64-darwin)
      racc (~> 1.4)
    oauth2 (2.0.17)
      faraday (>= 0.17.3, < 4.0)
      jwt (>= 1.0, < 4.0)
      logger (~> 1.2)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0, >= 2.0.3)
      version_gem (~> 1.1, >= 1.1.9)
    oj (3.16.11)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    omniauth (2.1.3)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-facebook (10.0.0)
      bigdecimal
      omniauth-oauth2 (>= 1.2, < 3)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    openssl (3.3.0)
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.3)
    pdf-core (0.10.0)
    prawn (2.5.0)
      matrix (~> 0.4)
      pdf-core (~> 0.10.0)
      ttfunk (~> 1.8)
    prawn-table (0.2.2)
      prawn (>= 1.3.0, < 3.0.0)
    public_suffix (5.1.1)
    puma (3.12.6)
    racc (1.8.1)
    rack (2.2.18)
    rack-cors (0.3.1)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-test (2.2.0)
      rack (>= 1.3)
    railroady (1.6.0)
    rails (*******)
      actioncable (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.3.0)
      railties (= *******)
      sprockets-rails (>= 2.0.0)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 0.8.7)
      thor (>= 0.19.0, < 2.0)
    rake (13.3.0)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    recursive-open-struct (2.0.0)
      ostruct
    redis (5.4.1)
      redis-client (>= 0.22.0)
    redis-client (0.26.1)
      connection_pool
    redis-namespace (1.11.0)
      redis (>= 4)
    reline (0.6.2)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    retriable (3.1.2)
    rexml (3.4.4)
    roo (2.7.1)
      nokogiri (~> 1)
      rubyzip (~> 1.1, < 2.0.0)
    roo-xls (1.2.0)
      nokogiri
      roo (>= 2.0.0, < 3)
      spreadsheet (> 0.9.0)
    rouge (4.6.1)
    rspec-core (3.9.3)
      rspec-support (~> 3.9.3)
    rspec-expectations (3.9.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.9.0)
    rspec-mocks (3.9.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.9.0)
    rspec-rails (3.9.1)
      actionpack (>= 3.0)
      activesupport (>= 3.0)
      railties (>= 3.0)
      rspec-core (~> 3.9.0)
      rspec-expectations (~> 3.9.0)
      rspec-mocks (~> 3.9.0)
      rspec-support (~> 3.9.0)
    rspec-support (3.9.4)
    ruby-kafka (1.5.0)
      digest-crc
    ruby-ole (********)
    ruby2_keywords (0.0.5)
    ruby_dep (1.5.0)
    rubyzip (1.1.7)
    s3_uploader (0.2.3)
      fog-aws (>= 0.9.4)
      mime-types
    sass (3.7.4)
      sass-listen (~> 4.0.0)
    sass-listen (4.0.0)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    securerandom (0.3.2)
    shopify_api (12.5.0)
      activesupport
      concurrent-ruby
      hash_diff
      httparty
      jwt
      oj
      openssl
      securerandom
      sorbet-runtime
      zeitwerk (~> 2.5, < 2.6.5)
    sidekiq (7.3.9)
      base64
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    snaky_hash (2.0.3)
      hashie (>= 0.1.0, < 6)
      version_gem (>= 1.1.8, < 3)
    sorbet-runtime (0.5.12443)
    spreadsheet (1.3.4)
      bigdecimal
      logger
      ruby-ole
    spring (2.1.1)
    spring-watcher-listen (2.0.1)
      listen (>= 2.7, < 4.0)
      spring (>= 1.2, < 3.0)
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    stripe (16.0.0)
    telegram-bot (0.16.7)
      actionpack (>= 4.0, < 8.1)
      activesupport (>= 4.0, < 8.1)
      httpclient (~> 2.7)
    temple (0.10.4)
    thor (1.4.0)
    thread_safe (0.3.6)
    tilt (2.6.1)
    timeout (0.4.3)
    trailblazer-option (0.1.2)
    ttfunk (1.8.0)
      bigdecimal (~> 3.1)
    tzinfo (1.2.11)
      thread_safe (~> 0.1)
    uber (0.1.0)
    version_gem (1.1.9)
    warden (1.2.9)
      rack (>= 2.0.9)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    write_xlsx (1.12.1)
      nkf
      rubyzip (>= 1.0.0)
    zeitwerk (2.6.4)

PLATFORMS
  arm64-darwin-25

DEPENDENCIES
  aws-sdk-s3
  axlsx (= 2.1.0.pre)
  axlsx_rails
  better_errors
  binding_of_caller
  bootsnap (>= 1.1.0)
  bumbler
  byebug
  colorize
  database_cleaner
  device_detector
  devise
  dotenv-rails
  exception_notification
  faker
  fog-aws
  friendly_id
  google-apis-identitytoolkit_v3
  googleauth
  haml
  jbuilder (~> 2.5)
  jwt
  kaminari
  kaminari-mongoid
  koala
  listen (>= 3.0.5, < 3.2)
  lograge
  mini_magick
  money
  money-rails (~> 1.12)
  mongoid (= 6.4.0)
  mongoid-autoinc
  mongoid-slug
  mongoid_enumerable
  msgpack (~> 1.4.0)
  myinfo!
  omniauth-facebook
  prawn
  prawn-table
  puma (~> 3.11)
  rack-cors (~> 0.3.1)
  railroady
  rails (~> 5.2.0)
  rails-controller-testing
  recursive-open-struct
  redis
  redis-namespace
  request_store
  rest-client
  roo-xls
  rspec-rails (~> 3.7)
  ruby-kafka
  s3_uploader
  sass
  shopify_api (~> 12.0)
  sidekiq
  spring
  spring-watcher-listen (~> 2.0.0)
  stringex!
  stripe
  telegram-bot
  tzinfo-data
  whenever
  write_xlsx

RUBY VERSION
   ruby 2.7.8p225

BUNDLED WITH
   2.4.22
